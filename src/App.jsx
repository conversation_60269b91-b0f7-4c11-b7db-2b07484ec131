import React, { useState, useEffect } from 'react'
import { invoke } from '@tauri-apps/api/core'
import './App.css'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [authState, setAuthState] = useState(null)

  useEffect(() => {
    // Check if already authenticated
    const accessToken = localStorage.getItem('spotify_access_token')
    if (accessToken) {
      setIsAuthenticated(true)
    }
  }, [])

  const handleSpotifyLogin = async () => {
    setLoading(true)
    setError('')

    try {
      // Use the complete authentication flow
      const authStateJson = await invoke('spotify_authenticate')
      const authState = JSON.parse(authStateJson)

      if (authState.access_token) {
        localStorage.setItem('spotify_access_token', authState.access_token)
        localStorage.setItem('spotify_refresh_token', authState.refresh_token)
        setIsAuthenticated(true)
        setAuthState(authState)
        setError('')
      } else {
        setError('Authentication failed: No access token received')
      }

    } catch (err) {
      console.error('Authentication error:', err)
      setError(`Authentication failed: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  const handleDebugDirectory = async () => {
    try {
      const debugInfo = await invoke('debug_working_directory')
      console.log('Debug info:', debugInfo)
      alert(debugInfo)
    } catch (err) {
      console.error('Debug error:', err)
      alert(`Debug failed: ${err}`)
    }
  }



  const handleLogout = () => {
    localStorage.removeItem('spotify_access_token')
    localStorage.removeItem('spotify_refresh_token')
    localStorage.removeItem('spotify_state')
    localStorage.removeItem('spotify_code_verifier')
    setIsAuthenticated(false)
    setAuthState(null)
  }

  return (
    <div className="app">
      <header className="header">
        <h1>🎵 Playlist Platformer</h1>
        <p>Bridge your Spotify playlists with Soulseek P2P file sharing</p>
      </header>

      <main>
        {!isAuthenticated ? (
          <div className="auth-section">
            <h2>Connect to Spotify</h2>
            <p>Sign in with your Spotify account to access your playlists</p>

            {error && <div className="error">{error}</div>}

            <div style={{ marginTop: '1rem' }}>
              <button
                className="auth-button"
                onClick={handleSpotifyLogin}
                disabled={loading}
              >
                {loading ? 'Authenticating...' : 'Login with Spotify'}
              </button>

              <button
                className="auth-button"
                onClick={handleDebugDirectory}
                style={{ marginLeft: '1rem', background: '#666' }}
              >
                Debug Directory
              </button>
            </div>

            <div style={{ marginTop: '1rem', fontSize: '0.9rem', color: '#666' }}>
              <p>Note: This requires a Spotify Premium account for full functionality</p>
            </div>
          </div>
        ) : (
          <div className="main-content">
            <div className="success">
              ✅ Successfully connected to Spotify!
            </div>

            <div style={{ marginTop: '2rem' }}>
              <h2>Your Playlists</h2>
              <p>Playlist loading functionality will be implemented in Phase 2</p>

              <button
                className="auth-button"
                onClick={handleLogout}
                style={{ background: '#666', marginTop: '1rem' }}
              >
                Logout
              </button>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
