use std::sync::Arc;
use tokio::sync::{oneshot, Mutex};
use warp::Filter;

pub struct CallbackServer {
    port: u16,
}

#[derive(Debug, Clone)]
pub struct CallbackResult {
    pub code: Option<String>,
    pub state: Option<String>,
    pub error: Option<String>,
}

impl CallbackServer {
    pub fn new(port: u16) -> Self {
        Self { port }
    }

    pub async fn start_and_wait(&self) -> Result<CallbackResult, Box<dyn std::error::Error + Send + Sync>> {
        let (tx, rx) = oneshot::channel::<CallbackResult>();
        let tx = Arc::new(Mutex::new(Some(tx)));

        // Create the callback route
        let callback = warp::path("callback")
            .and(warp::query::<std::collections::HashMap<String, String>>())
            .and(warp::any().map(move || tx.clone()))
            .and_then(handle_callback);

        // Create a simple success page route
        let success = warp::path("success")
            .map(|| {
                warp::reply::html(
                    r#"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Authentication Successful</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            .success { color: #1db954; font-size: 24px; margin-bottom: 20px; }
                            .message { color: #666; font-size: 16px; }
                        </style>
                    </head>
                    <body>
                        <div class="success">✅ Authentication Successful!</div>
                        <div class="message">You can now close this window and return to the application.</div>
                    </body>
                    </html>
                    "#
                )
            });

        let routes = callback.or(success);

        // Start the server
        let server = warp::serve(routes)
            .bind(([127, 0, 0, 1], self.port));

        // Run server and wait for callback
        tokio::select! {
            _ = server => {
                Err("Server stopped unexpectedly".into())
            }
            result = rx => {
                match result {
                    Ok(callback_result) => Ok(callback_result),
                    Err(_) => Err("Failed to receive callback".into())
                }
            }
        }
    }
}

async fn handle_callback(
    params: std::collections::HashMap<String, String>,
    tx: Arc<Mutex<Option<oneshot::Sender<CallbackResult>>>>,
) -> Result<impl warp::Reply, warp::Rejection> {
    let code = params.get("code").cloned();
    let state = params.get("state").cloned();
    let error = params.get("error").cloned();

    let result = CallbackResult { code, state, error };

    // Send the result through the channel
    if let Some(sender) = tx.lock().await.take() {
        let _ = sender.send(result);
    }

    // Redirect to success page
    Ok(warp::redirect::temporary(warp::http::Uri::from_static("/success")))
}
