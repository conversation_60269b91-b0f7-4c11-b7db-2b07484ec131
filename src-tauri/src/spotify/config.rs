use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct SpotifyConfig {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
}

impl SpotifyConfig {
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        // Get the current executable's directory and work backwards to find project root
        let exe_path = std::env::current_exe()?;
        let exe_dir = exe_path
            .parent()
            .ok_or("Could not get executable directory")?;

        // Common paths to try relative to executable
        let possible_paths = [
            // In development, the executable is usually in target/debug/
            exe_dir.join("../../spotify.settings.json"),
            exe_dir.join("../../../spotify.settings.json"),
            // Also try current working directory
            std::env::current_dir()?.join("spotify.settings.json"),
            // And some other common locations
            exe_dir.join("spotify.settings.json"),
            exe_dir.join("../spotify.settings.json"),
        ];

        let mut config_str = None;
        let mut last_error = None;
        let mut tried_paths = Vec::new();

        for path in &possible_paths {
            tried_paths.push(path.to_string_lossy().to_string());
            match std::fs::read_to_string(path) {
                Ok(content) => {
                    config_str = Some(content);
                    break;
                }
                Err(e) => {
                    last_error = Some(e);
                    continue;
                }
            }
        }

        let config_str = config_str.ok_or_else(|| {
            format!("Could not find spotify.settings.json in any of these locations: {:?}. Current working directory: {:?}. Executable path: {:?}. Last error: {:?}",
                    tried_paths, std::env::current_dir(), exe_path, last_error)
        })?;

        let config: serde_json::Value = serde_json::from_str(&config_str)?;

        Ok(SpotifyConfig {
            client_id: config["SPOTIFY_CLIENT_ID"]
                .as_str()
                .ok_or("SPOTIFY_CLIENT_ID not found in config")?
                .to_string(),
            client_secret: config["SPOTIFY_CLIENT_SECRET"]
                .as_str()
                .ok_or("SPOTIFY_CLIENT_SECRET not found in config")?
                .to_string(),
            redirect_uri: "http://localhost:8765/callback".to_string(),
        })
    }
}
