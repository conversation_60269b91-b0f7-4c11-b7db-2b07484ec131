{"$schema": "https://schema.tauri.app/config/2", "productName": "Playlist Platformer", "version": "0.1.0", "identifier": "com.playlistplatformer.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Playlist Platformer", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "plugins": {"shell": {"open": "^https://accounts\\.spotify\\.com/.*"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}