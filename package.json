{"name": "playlist-platformer", "version": "0.1.0", "description": "A desktop app that bridges Spotify playlists with Soulseek P2P file sharing", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "preview --host": "vite preview --host", "tauri": "tauri"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.0", "axios": "^1.6.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "vite": "^5.0.0"}}